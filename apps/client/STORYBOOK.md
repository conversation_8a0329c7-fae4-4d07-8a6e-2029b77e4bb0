# Storybook Setup for Bizzu Client

This document explains how to use Storybook to visualize and develop components in the Bizzu client application.

## What's Included

Storybook has been configured with the following features:

- **React + Vite**: Optimized for your existing Vite setup
- **TailwindCSS**: Your styles are automatically loaded
- **TypeScript**: Full TypeScript support
- **Path Aliases**: Your `@/` alias is configured and working
- **Accessibility Testing**: Built-in a11y addon for accessibility checks
- **Documentation**: Auto-generated docs for your components
- **Visual Testing**: Vitest integration for component testing

## Running Storybook

### Development Mode
```bash
npm run storybook
```
This starts Storybook on http://localhost:6006

### Build for Production
```bash
npm run build-storybook
```
This creates a static build in `storybook-static/`

## Creating Stories

Stories are located alongside your components. For example:

```
src/components/ui/
├── button.tsx
├── button.stories.ts  ← Story file
├── input.tsx
└── input.stories.ts   ← Story file
```

### Basic Story Structure

```typescript
import type { Meta, StoryObj } from "@storybook/react";
import { YourComponent } from "./yourComponent";

const meta = {
    title: "UI/YourComponent",
    component: YourComponent,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        // Define controls for props
        variant: {
            control: { type: "select" },
            options: ["primary", "secondary"],
        },
    },
} satisfies Meta<typeof YourComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        children: "Example",
    },
};
```

## Existing Stories

The following components already have stories created:

### UI Components
- **Button** (`src/components/ui/button.stories.ts`)
  - All variants: default, destructive, outline, secondary, ghost, link
  - All sizes: default, sm, lg, icon
  - Loading and disabled states

- **Input** (`src/components/ui/input.stories.ts`)
  - Different input types: text, email, password, number
  - Disabled state
  - Pre-filled values

### Logo Components
- **BizzuLogo** (`src/components/logo/bizzuLogo.stories.ts`)
  - All sizes: small, medium, large
  - Custom dimensions

## Best Practices

### 1. Organize Stories by Category
Use a consistent naming pattern for story titles:
- `UI/ComponentName` for basic UI components
- `Components/Category/ComponentName` for feature components
- `Layouts/ComponentName` for layout components

### 2. Include All Variants
Create stories for all possible states and variants of your components:
- Different prop combinations
- Loading states
- Error states
- Disabled states

### 3. Use Controls
Define argTypes to make your stories interactive:
```typescript
argTypes: {
    size: {
        control: { type: "select" },
        options: ["sm", "md", "lg"],
    },
    disabled: {
        control: { type: "boolean" },
    },
}
```

### 4. Add Documentation
Use the `tags: ["autodocs"]` to auto-generate documentation from your stories.

## Configuration Files

- `.storybook/main.ts` - Main Storybook configuration
- `.storybook/preview.ts` - Global decorators and parameters
- `.storybook/vitest.setup.ts` - Vitest integration setup

## Addons Included

- **@storybook/addon-docs** - Auto-generated documentation
- **@storybook/addon-a11y** - Accessibility testing
- **@storybook/addon-vitest** - Component testing integration
- **@chromatic-com/storybook** - Visual regression testing

## Tips

1. **Hot Reload**: Changes to your components and stories are automatically reflected
2. **Accessibility**: Check the "Accessibility" tab to see a11y violations
3. **Controls**: Use the "Controls" tab to interact with component props
4. **Docs**: View auto-generated documentation in the "Docs" tab
5. **Viewport**: Test responsive design using the viewport toolbar

## Next Steps

1. Create stories for more of your components in `src/components/`
2. Add stories for your form components (may require mock form context)
3. Create stories for your layout components
4. Set up visual regression testing with Chromatic (optional)

For more information, visit the [Storybook documentation](https://storybook.js.org/docs).
