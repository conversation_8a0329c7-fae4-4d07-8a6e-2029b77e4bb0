{"name": "bizzu-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint ./src", "prettier:fix": "prettier ./src --write", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.80.10", "@tanstack/react-router": "^1.121.24", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.2.1", "lucide-react": "^0.447.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.5.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^3.25.67"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.0", "@eslint/js": "^9.25.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@storybook/addon-a11y": "^9.1.2", "@storybook/addon-actions": "^9.0.8", "@storybook/addon-controls": "^9.0.8", "@storybook/addon-docs": "^9.1.2", "@storybook/addon-onboarding": "^9.1.2", "@storybook/react-vite": "^9.1.2", "@stylistic/eslint-plugin": "^5.2.2", "@tanstack/eslint-plugin-query": "^5.78.0", "@tanstack/router-plugin": "^1.121.25", "@types/node": "^22.17.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^9.1.2", "globals": "^16.0.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "sass-embedded": "^1.89.2", "storybook": "^9.1.2", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "engines": {"node": ">=22.16.0 < 23", "npm": ">=10.9.2 < 11"}}