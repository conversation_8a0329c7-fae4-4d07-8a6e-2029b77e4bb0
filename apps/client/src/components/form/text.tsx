import type { FieldValues, Path } from "react-hook-form";
import { Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

export type InputFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;

export function Text<T extends FieldValues>({
    name,
    label,
    startAdornment,
    endAdornment,
    ...rest
}: InputFieldProps<T>) {
    const { control, errors } = useFormContext<T>();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormField label={label} error={errors[name]?.message}>
                    <div className="relative">
                        {startAdornment && (
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                                {startAdornment}
                            </div>
                        )}
                        <Input
                            {...rest}
                            {...field}
                            className={[startAdornment ? "pl-9" : "", endAdornment ? "pr-9" : ""]
                                .filter(Boolean)
                                .join(" ")}
                        />
                        {endAdornment && (
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground">
                                {endAdornment}
                            </div>
                        )}
                    </div>
                </FormField>
            )}
        />
    );
}
