import type { FieldValues, Path } from "react-hook-form";
import { Controller } from "react-hook-form";
import { SelectContent, SelectItem, SelectTrigger, SelectValue, Select as UiSelect } from "@/components/ui/select";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type Option = { label: string; value: string };

type SelectFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
    options: Option[];
} & Omit<React.ComponentPropsWithoutRef<typeof UiSelect>, "value" | "onValueChange">;

export function SelectField<T extends FieldValues>({ name, label, options, ...rest }: SelectFieldProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <UiSelect value={(field.value as unknown as string) ?? ""} onValueChange={field.onChange} {...rest}>
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            {options.map((opt) => (
                                <SelectItem key={opt.value} value={opt.value}>
                                    {opt.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </UiSelect>
                </FormField>
            )}
        />
    );
}
