import type { FieldValues, Path } from "react-hook-form";
import { Controller } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type TextareaFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
} & React.TextareaHTMLAttributes<HTMLTextAreaElement>;

export function TextArea<T extends FieldValues>({ name, label, ...rest }: TextareaFieldProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <Textarea {...rest} {...field} />
                </FormField>
            )}
        />
    );
}
