import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { AlertCircle, CheckCircle, Info, AlertTriangle } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "./alert";

const meta = {
    title: "UI/Alert",
    component: Alert,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        variant: {
            control: { type: "select" },
            options: ["default", "destructive", "info", "success"],
        },
    },
} satisfies Meta<typeof Alert>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: (args) => (
        <Alert {...args}>
            <Info />
            <AlertTitle>Heads up!</AlertTitle>
            <AlertDescription>
                You can add components and dependencies to your app using the cli.
            </AlertDescription>
        </Alert>
    ),
    args: {
        variant: "default",
    },
};

export const Destructive: Story = {
    render: (args) => (
        <Alert {...args}>
            <AlertCircle />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
                Your session has expired. Please log in again.
            </AlertDescription>
        </Alert>
    ),
    args: {
        variant: "destructive",
    },
};

export const Info: Story = {
    render: (args) => (
        <Alert {...args}>
            <Info />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
                This is an informational message to help you understand the current state.
            </AlertDescription>
        </Alert>
    ),
    args: {
        variant: "info",
    },
};

export const Success: Story = {
    render: (args) => (
        <Alert {...args}>
            <CheckCircle />
            <AlertTitle>Success!</AlertTitle>
            <AlertDescription>
                Your changes have been saved successfully.
            </AlertDescription>
        </Alert>
    ),
    args: {
        variant: "success",
    },
};

export const WithoutIcon: Story = {
    render: (args) => (
        <Alert {...args}>
            <AlertTitle>No Icon Alert</AlertTitle>
            <AlertDescription>
                This alert doesn't have an icon, which is also perfectly fine.
            </AlertDescription>
        </Alert>
    ),
    args: {
        variant: "default",
    },
};

export const OnlyDescription: Story = {
    render: (args) => (
        <Alert {...args}>
            <AlertTriangle />
            <AlertDescription>
                This alert only has a description without a title.
            </AlertDescription>
        </Alert>
    ),
    args: {
        variant: "info",
    },
};
